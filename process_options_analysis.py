#!/usr/bin/env python3
"""
Universal Options Analysis Script

Defaults to SPX and processes all available data.
Can be run with a specific ticker to process that ticker's data instead.

Usage:
    python process_options_analysis.py              # Process all SPX data
    python process_options_analysis.py AAPL         # Process all AAPL data
    python process_options_analysis.py --ticker=TSLA # Process all TSLA data
"""

import sys
import os
import argparse
import glob
sys.path.insert(0, 'src')

from wall_analyzer import WallAnalyzer
from data_merger import DataMerger


class UniversalOptionsProcessor:
    """Universal processor for options analysis that works with any ticker."""
    
    def __init__(self, ticker: str = "SPX"):
        """
        Initialize the processor.
        
        Args:
            ticker: Stock ticker to process (default: SPX)
        """
        self.ticker = ticker.upper()
        self.analyzer = WallAnalyzer()
        self.merger = DataMerger()
        self.base_directory = "/Users/<USER>/Downloads/optionhistory"
    
    def find_ticker_quarters(self) -> list:
        """
        Find all available quarters for the specified ticker.
        
        Returns:
            List of (year, quarter) tuples
        """
        quarters = []
        
        if not os.path.exists(self.base_directory):
            print(f"Base directory does not exist: {self.base_directory}")
            return quarters
        
        print(f"Scanning for {self.ticker} data in: {self.base_directory}")
        
        # Find all quarter directories
        pattern = os.path.join(self.base_directory, "*_*_option_chain")
        quarter_dirs = glob.glob(pattern)
        
        for quarter_dir in quarter_dirs:
            dir_name = os.path.basename(quarter_dir)
            parts = dir_name.split('_')
            
            if len(parts) >= 2:
                year = parts[0]
                quarter = parts[1]
                
                # Check if this quarter has data for our ticker
                expected_complete_file = os.path.join(quarter_dir, f"{self.ticker.lower()}_complete_{year}_{quarter}.csv")
                
                if os.path.exists(expected_complete_file):
                    quarters.append((year, quarter))
                    print(f"Found {self.ticker} data: {year} {quarter}")
        
        quarters.sort(key=lambda x: (x[0], x[1]))
        return quarters
    
    def process_ticker_data(self) -> list:
        """
        Process all available data for the ticker.
        
        Returns:
            List of generated analysis file paths
        """
        print(f"Universal Options Analysis for {self.ticker}")
        print("=" * 60)
        
        # Find all quarters with data for this ticker
        quarters = self.find_ticker_quarters()
        
        if not quarters:
            print(f"No {self.ticker} data found in {self.base_directory}")
            print("Expected file format: {ticker}_complete_{year}_{quarter}.csv")
            return []
        
        print(f"Found {len(quarters)} quarters of {self.ticker} data: {quarters}")
        print()
        
        saved_files = []
        
        for year, quarter in quarters:
            try:
                print(f"Processing {self.ticker} {year} {quarter}...")
                
                # Find the complete file for this ticker/quarter
                quarter_dir = os.path.join(self.base_directory, f"{year}_{quarter}_option_chain")
                complete_file = os.path.join(quarter_dir, f"{self.ticker.lower()}_complete_{year}_{quarter}.csv")
                
                if not os.path.exists(complete_file):
                    print(f"Complete file not found: {complete_file}")
                    continue
                
                # Process the file
                daily_analysis = self.analyzer.process_complete_file(complete_file)
                
                if not daily_analysis.empty:
                    # Generate output filename
                    output_name = f"{self.ticker.lower()}_option_daily_analysis_{year}_{quarter}.csv"
                    output_path = os.path.join(quarter_dir, output_name)
                    
                    # Save the analysis
                    saved_path = self.analyzer.save_daily_analysis(daily_analysis, output_path)
                    saved_files.append(saved_path)
                    
                    # Print summary
                    print(f"✅ {self.ticker} {year} {quarter}:")
                    print(f"   - Trading days: {len(daily_analysis)}")
                    print(f"   - Date range: {daily_analysis['date'].min()} to {daily_analysis['date'].max()}")
                    print(f"   - Avg daily options: {daily_analysis['total_options'].mean():.0f}")
                    print(f"   - Avg daily notional: ${daily_analysis['total_notional'].mean():,.0f}")
                    print()
                
            except Exception as e:
                print(f"❌ Error processing {self.ticker} {year} {quarter}: {e}")
                continue
        
        return saved_files
    
    def generate_summary_report(self, saved_files: list):
        """
        Generate a summary report of all processed data.
        
        Args:
            saved_files: List of generated analysis file paths
        """
        print(f"{'='*60}")
        print(f"{self.ticker} OPTIONS ANALYSIS SUMMARY")
        print(f"{'='*60}")
        
        if not saved_files:
            print(f"❌ No {self.ticker} analysis files were generated.")
            return
        
        print(f"✅ Successfully processed {len(saved_files)} quarters of {self.ticker} data")
        print()
        
        total_size = 0
        total_days = 0
        
        print("Generated analysis files:")
        for i, file_path in enumerate(saved_files, 1):
            file_size = os.path.getsize(file_path) / 1024  # KB
            total_size += file_size
            
            # Count trading days in this file
            try:
                import pandas as pd
                df = pd.read_csv(file_path)
                days = len(df)
                total_days += days
                
                print(f"{i:2d}. {os.path.basename(file_path)} ({file_size:.1f} KB, {days} days)")
            except:
                print(f"{i:2d}. {os.path.basename(file_path)} ({file_size:.1f} KB)")
        
        print(f"\nSummary:")
        print(f"- Total files: {len(saved_files)}")
        print(f"- Total size: {total_size:.1f} KB")
        print(f"- Total trading days: {total_days}")
        print(f"- Average file size: {total_size/len(saved_files):.1f} KB")
        
        print(f"\nEach daily analysis includes:")
        print(f"- SPX/Index OHLC prices")
        print(f"- Put/Call walls (support/resistance levels)")
        print(f"- Gamma walls (gamma exposure levels)")
        print(f"- Notional values (call_notional, put_notional, total_notional)")
        print(f"- Daily aggregates (gamma, vega, open interest, volume)")


def main():
    """Main function with argument parsing."""
    parser = argparse.ArgumentParser(
        description="Universal Options Analysis - Defaults to SPX, can process any ticker",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python process_options_analysis.py              # Process all SPX data
  python process_options_analysis.py AAPL         # Process all AAPL data  
  python process_options_analysis.py --ticker=TSLA # Process all TSLA data
        """
    )
    
    parser.add_argument(
        'ticker', 
        nargs='?', 
        default='SPX',
        help='Stock ticker to process (default: SPX)'
    )
    
    parser.add_argument(
        '--ticker',
        dest='ticker_flag',
        help='Alternative way to specify ticker'
    )
    
    args = parser.parse_args()
    
    # Determine ticker (command line arg takes precedence over flag)
    ticker = args.ticker_flag if args.ticker_flag else args.ticker
    
    try:
        # Initialize processor
        processor = UniversalOptionsProcessor(ticker)
        
        # Process all data for the ticker
        saved_files = processor.process_ticker_data()
        
        # Generate summary report
        processor.generate_summary_report(saved_files)
        
        if saved_files:
            print(f"\n🎉 {ticker} options analysis complete!")
            return 0
        else:
            print(f"\n❌ No {ticker} data was processed.")
            return 1
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
