"""
Data merger module for combining SPX options data with SPX index price data.
"""

import pandas as pd
import os
import glob
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from config import get_config
from data_reader import DataReader


class DataMerger:
    """Merges SPX options data with SPX index price data."""
    
    def __init__(self):
        """Initialize the data merger with configuration."""
        self.config = get_config()
        self.reader = DataReader()
    
    def find_all_option_quarters(self, base_directory: str) -> List[Tuple[str, str]]:
        """
        Find all available option quarters by scanning the base directory.

        Args:
            base_directory: Base directory containing option data (/Users/<USER>/Downloads/optionhistory)

        Returns:
            List of (year, quarter) tuples
        """
        quarters = []

        if not os.path.exists(base_directory):
            print(f"Base directory does not exist: {base_directory}")
            return quarters

        print(f"Scanning directory: {base_directory}")

        # List all directories in the base directory
        try:
            for item in os.listdir(base_directory):
                item_path = os.path.join(base_directory, item)
                if os.path.isdir(item_path):
                    # Parse directory name: YYYY_QX_option_chain
                    parts = item.split('_')
                    if len(parts) >= 3 and parts[2] == 'option' and len(parts) >= 4 and parts[3] == 'chain':
                        year = parts[0]
                        quarter = parts[1]
                        quarters.append((year, quarter))
                        print(f"Found quarter: {year} {quarter} in directory {item}")
        except Exception as e:
            print(f"Error scanning directory {base_directory}: {e}")

        # Sort by year and quarter
        quarters.sort(key=lambda x: (x[0], x[1]))
        print(f"Total quarters found: {len(quarters)}")
        return quarters
    
    def load_index_data(self, ticker: str, timeperiod: str = '1day') -> Optional[pd.DataFrame]:
        """
        Load index price data for any ticker.

        Args:
            ticker: Ticker symbol (e.g., 'SPX', 'VIX')
            timeperiod: Time period for the data (default: '1day')

        Returns:
            DataFrame with index data or None if not found
        """
        print(f"Loading {ticker} index data for timeperiod: {timeperiod}")
        df = self.reader.read_securities_data(ticker, timeperiod)

        if df is not None:
            # Ensure date column is datetime
            df['date'] = pd.to_datetime(df['date'])
            # Add prefix to distinguish from option columns
            ticker_lower = ticker.lower()
            df = df.rename(columns={
                'open': f'{ticker_lower}_open',
                'high': f'{ticker_lower}_high',
                'low': f'{ticker_lower}_low',
                'close': f'{ticker_lower}_close'
            })
            print(f"Loaded {len(df)} rows of {ticker} index data")
            print(f"Date range: {df['date'].min()} to {df['date'].max()}")

        return df
    
    def merge_options_with_index(self, options_df: pd.DataFrame, index_df: pd.DataFrame, ticker: str) -> pd.DataFrame:
        """
        Merge options data with index data on date.

        Args:
            options_df: Options data DataFrame
            index_df: Index data DataFrame
            ticker: Ticker symbol for column naming

        Returns:
            Merged DataFrame
        """
        print("Merging options data with index data...")

        # Ensure date columns are datetime
        options_df['date'] = pd.to_datetime(options_df['date'])
        index_df['date'] = pd.to_datetime(index_df['date'])

        # Merge on date
        merged_df = pd.merge(options_df, index_df, on='date', how='left')

        print(f"Merged data: {len(merged_df)} rows")

        # Check for missing index data
        ticker_lower = ticker.lower()
        open_col = f'{ticker_lower}_open'
        if open_col in merged_df.columns:
            missing_index = merged_df[open_col].isna().sum()
            if missing_index > 0:
                print(f"Warning: {missing_index} rows have missing {ticker} index data")

        return merged_df
    
    def process_quarter(self, ticker: str, year: str, quarter: str, index_df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """
        Process a single quarter of options data and merge with index data.

        Args:
            ticker: Ticker symbol (e.g., 'SPX', 'VIX')
            year: Year (e.g., '2023')
            quarter: Quarter (e.g., 'Q2')
            index_df: Index data DataFrame

        Returns:
            Merged DataFrame or None if processing failed
        """
        print(f"\n=== Processing {ticker} {year} {quarter} ===")

        # Load options data for this quarter
        options_df = self.reader.read_options_data(ticker, year, quarter)

        if options_df is None:
            print(f"No {ticker} options data found for {year} {quarter}")
            return None

        # Merge with index data
        merged_df = self.merge_options_with_index(options_df, index_df, ticker)

        # Add metadata columns
        merged_df['data_year'] = year
        merged_df['data_quarter'] = quarter
        merged_df['processing_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        return merged_df
    
    def save_complete_data(self, df: pd.DataFrame, ticker: str, year: str, quarter: str, output_dir: str = None) -> str:
        """
        Save the complete merged data to a file.

        Args:
            df: Merged DataFrame to save
            ticker: Ticker symbol
            year: Year
            quarter: Quarter
            output_dir: Output directory (default: same as options directory)

        Returns:
            Path to saved file
        """
        if output_dir is None:
            # Use the same directory as the options data
            output_dir = self.config.format_directory_path('options', year=year, quarter=quarter)

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Generate filename
        filename = f"{ticker.lower()}_complete_{year}_{quarter}.csv"
        output_path = os.path.join(output_dir, filename)

        print(f"Saving complete data to: {output_path}")
        df.to_csv(output_path, index=False)

        print(f"Saved {len(df)} rows to {filename}")
        return output_path
    
    def process_all_quarters(self, base_directory: str, ticker: str = 'SPX') -> List[str]:
        """
        Process all quarters in the base directory for a specific ticker.

        Args:
            base_directory: Base directory containing option data
            ticker: Ticker symbol to process (default: 'SPX')

        Returns:
            List of paths to saved files
        """
        print(f"{ticker} Options and Index Data Merger")
        print("=" * 50)

        # Load index data once
        index_df = self.load_index_data(ticker, '1day')
        if index_df is None:
            raise ValueError(f"Could not load {ticker} index data. Please check the securities data configuration.")

        # Find all available quarters that have this ticker's data
        all_quarters = self.find_all_option_quarters(base_directory)

        # Filter to quarters that have data for this ticker
        ticker_quarters = []
        for year, quarter in all_quarters:
            options_df = self.reader.read_options_data(ticker, year, quarter)
            if options_df is not None:
                ticker_quarters.append((year, quarter))

        print(f"\nFound {len(ticker_quarters)} quarters with {ticker} data: {ticker_quarters}")

        saved_files = []

        for year, quarter in ticker_quarters:
            try:
                # Process this quarter
                merged_df = self.process_quarter(ticker, year, quarter, index_df)

                if merged_df is not None:
                    # Save the complete data
                    output_path = self.save_complete_data(merged_df, ticker, year, quarter)
                    saved_files.append(output_path)

                    # Print summary
                    print(f"Summary for {ticker} {year} {quarter}:")
                    print(f"  - Total rows: {len(merged_df)}")
                    print(f"  - Date range: {merged_df['date'].min()} to {merged_df['date'].max()}")
                    print(f"  - Unique dates: {merged_df['date'].nunique()}")
                    print(f"  - Call/Put distribution: {merged_df['Call/Put'].value_counts().to_dict()}")

            except Exception as e:
                print(f"Error processing {ticker} {year} {quarter}: {e}")
                continue

        print(f"\n=== {ticker} PROCESSING COMPLETE ===")
        print(f"Successfully processed {len(saved_files)} quarters")
        print("Saved files:")
        for file_path in saved_files:
            print(f"  - {file_path}")

        return saved_files


def main():
    """Main function to process all quarters."""
    merger = DataMerger()
    
    # Base directory for options data
    base_directory = "/Users/<USER>/Downloads/optionhistory"
    
    try:
        saved_files = merger.process_all_quarters(base_directory)
        print(f"\nAll processing complete! Generated {len(saved_files)} files.")
        
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
