"""
Configuration module for reading and managing data format configurations.
Handles instrument types, directory formats, file naming conventions, and data formats.
"""

import yaml
import os
from typing import Dict, List, Any, Optional
from pathlib import Path


class ConfigManager:
    """Manages configuration for different instrument types and data formats."""
    
    def __init__(self, config_file: str = "config.yaml"):
        """
        Initialize the configuration manager.
        
        Args:
            config_file: Path to the configuration YAML file
        """
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        config_path = Path(self.config_file)
        
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_file}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing YAML configuration: {e}")
        except Exception as e:
            raise RuntimeError(f"Error loading configuration file: {e}")
    
    def get_instrument_config(self, instrument_type: str) -> Dict[str, Any]:
        """
        Get configuration for a specific instrument type.
        
        Args:
            instrument_type: Type of instrument (e.g., 'options', 'securities')
            
        Returns:
            Dictionary containing instrument configuration
            
        Raises:
            KeyError: If instrument type is not found in configuration
        """
        if instrument_type not in self.config.get('instruments', {}):
            available = list(self.config.get('instruments', {}).keys())
            raise KeyError(f"Instrument type '{instrument_type}' not found. Available: {available}")
        
        return self.config['instruments'][instrument_type]
    
    def get_directory_format(self, instrument_type: str) -> str:
        """Get directory format for an instrument type."""
        return self.get_instrument_config(instrument_type)['directory_format']
    
    def get_file_name_format(self, instrument_type: str) -> str:
        """Get file name format for an instrument type."""
        return self.get_instrument_config(instrument_type)['file_name_format']
    
    def get_file_type_format(self, instrument_type: str) -> str:
        """Get file type format for an instrument type."""
        return self.get_instrument_config(instrument_type)['file_type_format']
    
    def get_data_format(self, instrument_type: str) -> Dict[str, Any]:
        """Get data format configuration for an instrument type."""
        return self.get_instrument_config(instrument_type)['data_format']
    
    def get_columns(self, instrument_type: str) -> List[str]:
        """Get column names for an instrument type."""
        return self.get_data_format(instrument_type)['columns']
    
    def get_separator(self, instrument_type: str) -> str:
        """Get field separator for an instrument type."""
        return self.get_data_format(instrument_type).get('separator', ',')
    
    def has_header(self, instrument_type: str) -> bool:
        """Check if files for an instrument type have headers."""
        return self.get_data_format(instrument_type).get('header', False)
    
    def get_timeperiods(self, instrument_type: str) -> Optional[List[str]]:
        """Get available timeperiods for an instrument type (if applicable)."""
        config = self.get_instrument_config(instrument_type)
        return config.get('timeperiods')
    
    def get_available_instruments(self) -> List[str]:
        """Get list of available instrument types."""
        return list(self.config.get('instruments', {}).keys())
    
    def get_default_instrument(self) -> str:
        """Get the default instrument type."""
        return self.config.get('settings', {}).get('default_instrument', 'options')
    
    def format_directory_path(self, instrument_type: str, **kwargs) -> str:
        """
        Format directory path with provided parameters.
        
        Args:
            instrument_type: Type of instrument
            **kwargs: Parameters to substitute in the format string
            
        Returns:
            Formatted directory path
        """
        format_string = self.get_directory_format(instrument_type)
        try:
            return format_string.format(**kwargs)
        except KeyError as e:
            raise ValueError(f"Missing required parameter for directory format: {e}")
    
    def format_file_name(self, instrument_type: str, **kwargs) -> str:
        """
        Format file name with provided parameters.
        
        Args:
            instrument_type: Type of instrument
            **kwargs: Parameters to substitute in the format string
            
        Returns:
            Formatted file name
        """
        format_string = self.get_file_name_format(instrument_type)
        try:
            return format_string.format(**kwargs)
        except KeyError as e:
            raise ValueError(f"Missing required parameter for file name format: {e}")
    
    def get_full_file_path(self, instrument_type: str, **kwargs) -> str:
        """
        Get complete file path by combining directory and file name formats.
        
        Args:
            instrument_type: Type of instrument
            **kwargs: Parameters to substitute in the format strings
            
        Returns:
            Complete file path
        """
        directory = self.format_directory_path(instrument_type, **kwargs)
        filename = self.format_file_name(instrument_type, **kwargs)
        return os.path.join(directory, filename)


# Global configuration instance
config_manager = ConfigManager()


def get_config() -> ConfigManager:
    """Get the global configuration manager instance."""
    return config_manager
