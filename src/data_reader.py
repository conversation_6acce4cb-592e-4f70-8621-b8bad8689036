"""
Data reader module for loading options and securities data using the configuration system.
"""

import pandas as pd
import os
from typing import List, Dict, Any, Optional
from config import get_config


class DataReader:
    """Reads financial data files using the configuration system."""
    
    def __init__(self):
        """Initialize the data reader with configuration."""
        self.config = get_config()
    
    def find_options_files(self, ticker: str, year: str, quarter: str) -> List[str]:
        """
        Find all possible options files for a given ticker, year, and quarter.

        Args:
            ticker: Stock ticker (e.g., 'SPX')
            year: Year (e.g., '2023')
            quarter: Quarter (e.g., 'Q1')

        Returns:
            List of file paths that match the pattern
        """
        # Get the directory path directly
        directory = self.config.format_directory_path('options',
                                                     year=year,
                                                     quarter=quarter)

        # Generate the expected filename
        filename = self.config.format_file_name('options',
                                               ticker=ticker,
                                               year=year,
                                               quarter=quarter)

        file_path = os.path.join(directory, filename)

        files = []
        if os.path.exists(file_path):
            files.append(file_path)

        return files
    
    def read_options_data(self, ticker: str, year: str, quarter: str) -> Optional[pd.DataFrame]:
        """
        Read options data for a specific ticker, year, and quarter.
        
        Args:
            ticker: Stock ticker (e.g., 'SPX')
            year: Year (e.g., '2023')
            quarter: Quarter (e.g., 'Q1')
            
        Returns:
            DataFrame with options data or None if no files found
        """
        files = self.find_options_files(ticker, year, quarter)
        
        if not files:
            print(f"No options files found for {ticker} {year} {quarter}")
            return None
        
        print(f"Found {len(files)} options file(s) for {ticker} {year} {quarter}:")
        for file in files:
            print(f"  - {file}")
        
        # Read all files and combine them
        dataframes = []
        columns = self.config.get_columns('options')
        separator = self.config.get_separator('options')
        has_header = self.config.has_header('options')
        
        for file_path in files:
            try:
                if has_header:
                    df = pd.read_csv(file_path, sep=separator)
                else:
                    df = pd.read_csv(file_path, sep=separator, names=columns)
                
                # Add source file information
                df['source_file'] = os.path.basename(file_path)
                dataframes.append(df)
                print(f"Successfully loaded {len(df)} rows from {os.path.basename(file_path)}")
                
            except Exception as e:
                print(f"Error reading {file_path}: {e}")
        
        if not dataframes:
            return None
        
        # Combine all dataframes
        combined_df = pd.concat(dataframes, ignore_index=True)
        print(f"Total combined rows: {len(combined_df)}")
        
        return combined_df
    
    def read_securities_data(self, ticker: str, timeperiod: str) -> Optional[pd.DataFrame]:
        """
        Read securities data for a specific ticker and timeperiod.
        
        Args:
            ticker: Stock ticker (e.g., 'SPX')
            timeperiod: Time period (e.g., '1day', '1min', '5min', '30min')
            
        Returns:
            DataFrame with securities data or None if file not found
        """
        file_path = self.config.get_full_file_path('securities', 
                                                  ticker=ticker, 
                                                  timeperiod=timeperiod)
        
        if not os.path.exists(file_path):
            print(f"Securities file not found: {file_path}")
            return None
        
        columns = self.config.get_columns('securities')
        separator = self.config.get_separator('securities')
        has_header = self.config.has_header('securities')
        
        try:
            if has_header:
                df = pd.read_csv(file_path, sep=separator)
            else:
                df = pd.read_csv(file_path, sep=separator, names=columns)
            
            print(f"Successfully loaded {len(df)} rows from {os.path.basename(file_path)}")
            return df
            
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            return None
    
    def get_data_summary(self, df: pd.DataFrame, data_type: str) -> Dict[str, Any]:
        """
        Get a summary of the loaded data.
        
        Args:
            df: DataFrame to summarize
            data_type: Type of data ('options' or 'securities')
            
        Returns:
            Dictionary with summary statistics
        """
        if df is None or df.empty:
            return {"error": "No data to summarize"}
        
        summary = {
            "total_rows": len(df),
            "columns": list(df.columns),
            "date_range": None,
            "memory_usage": f"{df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB"
        }
        
        # Try to get date range
        if 'date' in df.columns:
            try:
                df['date'] = pd.to_datetime(df['date'])
                summary["date_range"] = {
                    "start": df['date'].min().strftime('%Y-%m-%d'),
                    "end": df['date'].max().strftime('%Y-%m-%d')
                }
            except:
                summary["date_range"] = "Could not parse dates"
        
        # Add specific summaries based on data type
        if data_type == 'options':
            if 'Call/Put' in df.columns:
                summary["call_put_distribution"] = df['Call/Put'].value_counts().to_dict()
            if 'Strike' in df.columns:
                summary["strike_range"] = {
                    "min": df['Strike'].min(),
                    "max": df['Strike'].max()
                }
        
        return summary


def read_spx_options_2023_q1() -> Optional[pd.DataFrame]:
    """
    Convenience function to read SPX options data for 2023 Q1.
    
    Returns:
        DataFrame with SPX options data or None if not found
    """
    reader = DataReader()
    return reader.read_options_data('SPX', '2023', 'Q1')


def main():
    """Main function to demonstrate reading SPX options for 2023 Q1."""
    print("Reading SPX Options Data for 2023 Q1")
    print("=" * 50)
    
    # Try to read SPX options data
    df = read_spx_options_2023_q1()
    
    if df is not None:
        reader = DataReader()
        summary = reader.get_data_summary(df, 'options')
        
        print("\nData Summary:")
        print(f"Total rows: {summary['total_rows']}")
        print(f"Columns: {summary['columns']}")
        print(f"Date range: {summary.get('date_range', 'N/A')}")
        print(f"Memory usage: {summary['memory_usage']}")
        
        if 'call_put_distribution' in summary:
            print(f"Call/Put distribution: {summary['call_put_distribution']}")
        
        if 'strike_range' in summary:
            print(f"Strike range: {summary['strike_range']['min']} - {summary['strike_range']['max']}")
        
        # Show first few rows
        print("\nFirst 5 rows:")
        print(df.head())
        
    else:
        print("No SPX options data found for 2023 Q1")
        print("\nTo use this script, ensure your options data files are located at:")
        
        config = get_config()
        example_dir = config.format_directory_path('options', 
                                                  year='2023', 
                                                  quarter='Q1', 
                                                  random='EXAMPLE')
        example_file = config.format_file_name('options', 
                                              ticker='SPX', 
                                              year='2023', 
                                              quarter='Q1')
        
        print(f"Directory pattern: {example_dir}")
        print(f"File name: {example_file}")


if __name__ == "__main__":
    main()
