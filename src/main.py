"""
Main Options Analysis Application

Defaults to SPX and processes all available data.
Can be run with a specific ticker to process that ticker's data instead.

Usage:
    python src/main.py              # Process all SPX data
    python src/main.py AAPL         # Process all AAPL data
    python src/main.py --ticker=TSLA # Process all TSLA data
"""

import sys
import os
import argparse
import glob
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import get_config
from wall_analyzer import WallAnalyzer
from data_merger import DataMerger


class UniversalOptionsProcessor:
    """Universal processor for options analysis that works with any ticker."""

    def __init__(self, ticker: str = "SPX"):
        """
        Initialize the processor.

        Args:
            ticker: Stock ticker to process (default: SPX)
        """
        self.ticker = ticker.upper()
        self.analyzer = WallAnalyzer()
        self.merger = DataMerger()
        self.base_directory = "/Users/<USER>/Downloads/optionhistory"

    def find_ticker_quarters(self) -> list:
        """
        Find all available quarters for the specified ticker.

        Returns:
            List of (year, quarter) tuples
        """
        quarters = []

        if not os.path.exists(self.base_directory):
            print(f"Base directory does not exist: {self.base_directory}")
            return quarters

        print(f"Scanning for {self.ticker} data in: {self.base_directory}")

        # Find all quarter directories
        pattern = os.path.join(self.base_directory, "*_*_option_chain")
        quarter_dirs = glob.glob(pattern)

        for quarter_dir in quarter_dirs:
            dir_name = os.path.basename(quarter_dir)
            parts = dir_name.split('_')

            if len(parts) >= 2:
                year = parts[0]
                quarter = parts[1]

                # Check if this quarter has data for our ticker
                expected_complete_file = os.path.join(quarter_dir, f"{self.ticker.lower()}_complete_{year}_{quarter}.csv")

                if os.path.exists(expected_complete_file):
                    quarters.append((year, quarter))
                    print(f"Found {self.ticker} data: {year} {quarter}")

        quarters.sort(key=lambda x: (x[0], x[1]))
        return quarters

    def ensure_complete_files_exist(self) -> bool:
        """
        Ensure complete files exist by running the data merger if needed.

        Returns:
            True if complete files exist or were created successfully
        """
        # Check if any complete files exist
        quarters = self.find_ticker_quarters()

        if quarters:
            print(f"Found existing complete files for {len(quarters)} quarters")
            return True

        print(f"No complete files found for {self.ticker}. Running data merger...")

        # Run the data merger to create complete files
        try:
            saved_files = self.merger.process_all_quarters(self.base_directory)
            if saved_files:
                print(f"✅ Data merger created {len(saved_files)} complete files")
                return True
            else:
                print("❌ Data merger failed to create complete files")
                return False
        except Exception as e:
            print(f"❌ Error running data merger: {e}")
            return False

    def process_ticker_data(self) -> list:
        """
        Process all available data for the ticker.

        Returns:
            List of generated analysis file paths
        """
        print(f"Universal Options Analysis for {self.ticker}")
        print("=" * 60)

        # Ensure complete files exist (create them if needed)
        if not self.ensure_complete_files_exist():
            print(f"Cannot proceed without complete files for {self.ticker}")
            return []

        # Find all quarters with data for this ticker
        quarters = self.find_ticker_quarters()

        if not quarters:
            print(f"No {self.ticker} data found in {self.base_directory}")
            print("Expected file format: {ticker}_complete_{year}_{quarter}.csv")
            return []

        print(f"Found {len(quarters)} quarters of {self.ticker} data: {quarters}")
        print()

        saved_files = []

        for year, quarter in quarters:
            try:
                print(f"Processing {self.ticker} {year} {quarter}...")

                # Find the complete file for this ticker/quarter
                quarter_dir = os.path.join(self.base_directory, f"{year}_{quarter}_option_chain")
                complete_file = os.path.join(quarter_dir, f"{self.ticker.lower()}_complete_{year}_{quarter}.csv")

                if not os.path.exists(complete_file):
                    print(f"Complete file not found: {complete_file}")
                    continue

                # Process the file
                daily_analysis = self.analyzer.process_complete_file(complete_file)

                if not daily_analysis.empty:
                    # Generate output filename
                    output_name = f"{self.ticker.lower()}_option_daily_analysis_{year}_{quarter}.csv"
                    output_path = os.path.join(quarter_dir, output_name)

                    # Save the analysis
                    saved_path = self.analyzer.save_daily_analysis(daily_analysis, output_path)
                    saved_files.append(saved_path)

                    # Print summary
                    print(f"✅ {self.ticker} {year} {quarter}:")
                    print(f"   - Trading days: {len(daily_analysis)}")
                    print(f"   - Date range: {daily_analysis['date'].min()} to {daily_analysis['date'].max()}")
                    print(f"   - Avg daily options: {daily_analysis['total_options'].mean():.0f}")
                    print(f"   - Avg daily notional: ${daily_analysis['total_notional'].mean():,.0f}")
                    print()

            except Exception as e:
                print(f"❌ Error processing {self.ticker} {year} {quarter}: {e}")
                continue

        return saved_files

    def generate_summary_report(self, saved_files: list):
        """
        Generate a summary report of all processed data.

        Args:
            saved_files: List of generated analysis file paths
        """
        print(f"{'='*60}")
        print(f"{self.ticker} OPTIONS ANALYSIS SUMMARY")
        print(f"{'='*60}")

        if not saved_files:
            print(f"❌ No {self.ticker} analysis files were generated.")
            return

        print(f"✅ Successfully processed {len(saved_files)} quarters of {self.ticker} data")
        print()

        total_size = 0
        total_days = 0

        print("Generated analysis files:")
        for i, file_path in enumerate(saved_files, 1):
            file_size = os.path.getsize(file_path) / 1024  # KB
            total_size += file_size

            # Count trading days in this file
            try:
                import pandas as pd
                df = pd.read_csv(file_path)
                days = len(df)
                total_days += days

                print(f"{i:2d}. {os.path.basename(file_path)} ({file_size:.1f} KB, {days} days)")
            except:
                print(f"{i:2d}. {os.path.basename(file_path)} ({file_size:.1f} KB)")

        print(f"\nSummary:")
        print(f"- Total files: {len(saved_files)}")
        print(f"- Total size: {total_size:.1f} KB")
        print(f"- Total trading days: {total_days}")
        print(f"- Average file size: {total_size/len(saved_files):.1f} KB")

        print(f"\nEach daily analysis includes:")
        print(f"- SPX/Index OHLC prices")
        print(f"- Put/Call walls (support/resistance levels)")
        print(f"- Gamma walls (gamma exposure levels)")
        print(f"- Notional values (call_notional, put_notional, total_notional)")
        print(f"- Daily aggregates (gamma, vega, open interest, volume)")


def main():
    """Main function with argument parsing."""
    parser = argparse.ArgumentParser(
        description="Universal Options Analysis - Defaults to SPX, can process any ticker",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python src/main.py              # Process all SPX data
  python src/main.py AAPL         # Process all AAPL data
  python src/main.py --ticker=TSLA # Process all TSLA data
        """
    )

    parser.add_argument(
        'tickers',
        nargs='*',
        default=['SPX'],
        help='Stock ticker(s) to process (default: SPX). Can specify multiple: SPX VIX'
    )

    parser.add_argument(
        '--ticker',
        dest='ticker_flag',
        help='Alternative way to specify single ticker'
    )

    parser.add_argument(
        '--tickers',
        dest='tickers_flag',
        nargs='+',
        help='Alternative way to specify multiple tickers'
    )

    args = parser.parse_args()

    # Determine tickers (flags take precedence over positional args)
    if args.tickers_flag:
        tickers = args.tickers_flag
    elif args.ticker_flag:
        tickers = [args.ticker_flag]
    else:
        tickers = args.tickers

    # Ensure we have a list and convert to uppercase
    if isinstance(tickers, str):
        tickers = [tickers]
    tickers = [ticker.upper() for ticker in tickers]

    print(f"Processing tickers: {', '.join(tickers)}")
    print("=" * 60)

    all_saved_files = []
    successful_tickers = []
    failed_tickers = []

    try:
        for ticker in tickers:
            print(f"\n{'='*20} PROCESSING {ticker} {'='*20}")

            # Initialize processor for this ticker
            processor = UniversalOptionsProcessor(ticker)

            # Process all data for the ticker
            saved_files = processor.process_ticker_data()

            if saved_files:
                all_saved_files.extend(saved_files)
                successful_tickers.append(ticker)
                print(f"✅ {ticker} processing complete: {len(saved_files)} files generated")
            else:
                failed_tickers.append(ticker)
                print(f"❌ {ticker} processing failed: no data found")

        # Generate overall summary
        print(f"\n{'='*60}")
        print(f"OVERALL PROCESSING SUMMARY")
        print(f"{'='*60}")

        if successful_tickers:
            print(f"✅ Successfully processed: {', '.join(successful_tickers)}")
            print(f"📊 Total analysis files generated: {len(all_saved_files)}")

            # Show breakdown by ticker
            for ticker in successful_tickers:
                ticker_files = [f for f in all_saved_files if ticker.lower() in f.lower()]
                print(f"   - {ticker}: {len(ticker_files)} files")

        if failed_tickers:
            print(f"❌ Failed to process: {', '.join(failed_tickers)}")

        if successful_tickers:
            print(f"\n🎉 Multi-ticker options analysis complete!")
            print(f"Processed {len(successful_tickers)} out of {len(tickers)} tickers successfully.")
            return 0
        else:
            print(f"\n❌ No ticker data was processed successfully.")
            return 1

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
