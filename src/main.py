"""
Main application file demonstrating the configuration system usage.
"""

import sys
import os
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import get_config


def demonstrate_options_config():
    """Demonstrate options configuration usage."""
    print("=== OPTIONS CONFIGURATION ===")
    config = get_config()
    
    # Get options configuration
    instrument_type = "options"
    
    print(f"Instrument Type: {instrument_type}")
    print(f"Directory Format: {config.get_directory_format(instrument_type)}")
    print(f"File Name Format: {config.get_file_name_format(instrument_type)}")
    print(f"File Type: {config.get_file_type_format(instrument_type)}")
    print(f"Columns: {config.get_columns(instrument_type)}")
    print(f"Separator: '{config.get_separator(instrument_type)}'")
    print(f"Has Header: {config.has_header(instrument_type)}")
    
    # Example of formatting paths
    print("\n--- Example Path Formatting ---")
    example_params = {
        'year': '2024',
        'quarter': 'Q1',
        'random': 'abc123',
        'ticker': 'AAPL'
    }
    
    directory_path = config.format_directory_path(instrument_type, **example_params)
    file_name = config.format_file_name(instrument_type, **example_params)
    full_path = config.get_full_file_path(instrument_type, **example_params)
    
    print(f"Directory: {directory_path}")
    print(f"File Name: {file_name}")
    print(f"Full Path: {full_path}")


def demonstrate_securities_config():
    """Demonstrate securities configuration usage."""
    print("\n=== SECURITIES CONFIGURATION ===")
    config = get_config()
    
    # Get securities configuration
    instrument_type = "securities"
    
    print(f"Instrument Type: {instrument_type}")
    print(f"Directory Format: {config.get_directory_format(instrument_type)}")
    print(f"File Name Format: {config.get_file_name_format(instrument_type)}")
    print(f"File Type: {config.get_file_type_format(instrument_type)}")
    print(f"Columns: {config.get_columns(instrument_type)}")
    print(f"Separator: '{config.get_separator(instrument_type)}'")
    print(f"Has Header: {config.has_header(instrument_type)}")
    print(f"Available Timeperiods: {config.get_timeperiods(instrument_type)}")
    
    # Example of formatting paths for different timeperiods
    print("\n--- Example Path Formatting ---")
    example_params = {
        'ticker': 'AAPL'
    }
    
    timeperiods = config.get_timeperiods(instrument_type)
    for timeperiod in timeperiods:
        params = {**example_params, 'timeperiod': timeperiod}
        full_path = config.get_full_file_path(instrument_type, **params)
        print(f"Timeperiod {timeperiod}: {full_path}")


def demonstrate_general_config():
    """Demonstrate general configuration features."""
    print("\n=== GENERAL CONFIGURATION ===")
    config = get_config()
    
    print(f"Available Instruments: {config.get_available_instruments()}")
    print(f"Default Instrument: {config.get_default_instrument()}")


def main():
    """Main function to demonstrate configuration usage."""
    try:
        print("Configuration System Demonstration")
        print("=" * 50)
        
        demonstrate_options_config()
        demonstrate_securities_config()
        demonstrate_general_config()
        
        print("\n" + "=" * 50)
        print("Configuration system loaded successfully!")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
