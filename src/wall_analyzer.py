"""
Wall analyzer module for calculating daily put/call walls and gamma walls from SPX options data.
"""

import pandas as pd
import numpy as np
import os
import glob
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from config import get_config


class WallAnalyzer:
    """Analyzes SPX options data to calculate daily put/call walls and gamma walls."""
    
    def __init__(self):
        """Initialize the wall analyzer with configuration."""
        self.config = get_config()
    
    def find_complete_files(self, base_directory: str) -> List[str]:
        """
        Find all spx_complete files in the base directory.
        
        Args:
            base_directory: Base directory containing option data
            
        Returns:
            List of complete file paths
        """
        complete_files = []
        
        # Search for spx_complete files in all subdirectories
        pattern = os.path.join(base_directory, "**", "spx_complete_*.csv")
        complete_files = glob.glob(pattern, recursive=True)
        
        complete_files.sort()
        print(f"Found {len(complete_files)} complete files")
        
        return complete_files
    
    def calculate_put_call_walls(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate put and call walls based on open interest.
        
        Args:
            df: DataFrame with options data for a single day
            
        Returns:
            Dictionary with wall calculations
        """
        # Separate calls and puts
        calls = df[df['Call/Put'] == 'c'].copy()
        puts = df[df['Call/Put'] == 'p'].copy()
        
        # Calculate call wall (strike with highest call open interest)
        call_wall_strike = 0
        call_wall_oi = 0
        if not calls.empty:
            call_oi_by_strike = calls.groupby('Strike')['Open Interest'].sum()
            if not call_oi_by_strike.empty:
                call_wall_strike = call_oi_by_strike.idxmax()
                call_wall_oi = call_oi_by_strike.max()
        
        # Calculate put wall (strike with highest put open interest)
        put_wall_strike = 0
        put_wall_oi = 0
        if not puts.empty:
            put_oi_by_strike = puts.groupby('Strike')['Open Interest'].sum()
            if not put_oi_by_strike.empty:
                put_wall_strike = put_oi_by_strike.idxmax()
                put_wall_oi = put_oi_by_strike.max()
        
        return {
            'call_wall_strike': call_wall_strike,
            'call_wall_oi': call_wall_oi,
            'put_wall_strike': put_wall_strike,
            'put_wall_oi': put_wall_oi
        }
    
    def calculate_gamma_walls(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate gamma walls based on gamma exposure.
        
        Args:
            df: DataFrame with options data for a single day
            
        Returns:
            Dictionary with gamma wall calculations
        """
        # Separate calls and puts
        calls = df[df['Call/Put'] == 'c'].copy()
        puts = df[df['Call/Put'] == 'p'].copy()
        
        # Calculate gamma exposure for calls (positive gamma exposure)
        call_gamma_wall_strike = 0
        call_gamma_wall_exposure = 0
        if not calls.empty:
            calls['gamma_exposure'] = calls['Gamma'] * calls['Open Interest']
            call_gamma_by_strike = calls.groupby('Strike')['gamma_exposure'].sum()
            if not call_gamma_by_strike.empty:
                call_gamma_wall_strike = call_gamma_by_strike.idxmax()
                call_gamma_wall_exposure = call_gamma_by_strike.max()
        
        # Calculate gamma exposure for puts (negative gamma exposure for market makers)
        put_gamma_wall_strike = 0
        put_gamma_wall_exposure = 0
        if not puts.empty:
            puts['gamma_exposure'] = puts['Gamma'] * puts['Open Interest']
            put_gamma_by_strike = puts.groupby('Strike')['gamma_exposure'].sum()
            if not put_gamma_by_strike.empty:
                put_gamma_wall_strike = put_gamma_by_strike.idxmax()
                put_gamma_wall_exposure = put_gamma_by_strike.max()
        
        return {
            'gamma_call_wall_strike': call_gamma_wall_strike,
            'gamma_call_wall_exposure': call_gamma_wall_exposure,
            'gamma_put_wall_strike': put_gamma_wall_strike,
            'gamma_put_wall_exposure': put_gamma_wall_exposure
        }
    
    def calculate_daily_aggregates(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate daily aggregate metrics.
        
        Args:
            df: DataFrame with options data for a single day
            
        Returns:
            Dictionary with aggregate metrics
        """
        return {
            'total_gamma': df['Gamma'].sum(),
            'total_vega': df['Vega'].sum(),
            'total_open_interest': df['Open Interest'].sum(),
            'total_volume': df['Volume'].sum(),
            'unique_strikes': df['Strike'].nunique(),
            'total_options': len(df),
            'call_count': len(df[df['Call/Put'] == 'c']),
            'put_count': len(df[df['Call/Put'] == 'p'])
        }
    
    def analyze_daily_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Analyze options data and create daily summary.
        
        Args:
            df: DataFrame with complete options data
            
        Returns:
            DataFrame with daily analysis
        """
        print(f"Analyzing {len(df)} rows of options data...")
        
        # Ensure date column is datetime
        df['date'] = pd.to_datetime(df['date'])
        
        # Group by date and analyze each day
        daily_results = []
        
        for date, day_data in df.groupby('date'):
            print(f"Processing {date.strftime('%Y-%m-%d')} with {len(day_data)} options")
            
            # Get SPX price data (should be the same for all rows on this date)
            spx_data = day_data.iloc[0]
            
            # Calculate walls
            pc_walls = self.calculate_put_call_walls(day_data)
            gamma_walls = self.calculate_gamma_walls(day_data)
            aggregates = self.calculate_daily_aggregates(day_data)
            
            # Combine all metrics
            daily_result = {
                'date': date,
                'spx_open': spx_data['spx_open'],
                'spx_high': spx_data['spx_high'],
                'spx_low': spx_data['spx_low'],
                'spx_close': spx_data['spx_close'],
                
                # Put/Call Walls
                'put_wall_strike': pc_walls['put_wall_strike'],
                'put_wall_oi': pc_walls['put_wall_oi'],
                'call_wall_strike': pc_walls['call_wall_strike'],
                'call_wall_oi': pc_walls['call_wall_oi'],
                
                # Gamma Walls
                'gamma_put_wall_strike': gamma_walls['gamma_put_wall_strike'],
                'gamma_put_wall_exposure': gamma_walls['gamma_put_wall_exposure'],
                'gamma_call_wall_strike': gamma_walls['gamma_call_wall_strike'],
                'gamma_call_wall_exposure': gamma_walls['gamma_call_wall_exposure'],
                
                # Aggregates
                'total_gamma': aggregates['total_gamma'],
                'total_vega': aggregates['total_vega'],
                'total_open_interest': aggregates['total_open_interest'],
                'total_volume': aggregates['total_volume'],
                'unique_strikes': aggregates['unique_strikes'],
                'total_options': aggregates['total_options'],
                'call_count': aggregates['call_count'],
                'put_count': aggregates['put_count'],
                
                # Metadata
                'data_year': spx_data['data_year'],
                'data_quarter': spx_data['data_quarter'],
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            daily_results.append(daily_result)
        
        # Convert to DataFrame
        result_df = pd.DataFrame(daily_results)
        result_df = result_df.sort_values('date')
        
        print(f"Generated daily analysis for {len(result_df)} trading days")
        return result_df
    
    def process_complete_file(self, file_path: str) -> pd.DataFrame:
        """
        Process a single complete file and generate daily analysis.
        
        Args:
            file_path: Path to the complete CSV file
            
        Returns:
            DataFrame with daily analysis
        """
        print(f"\nProcessing: {os.path.basename(file_path)}")
        
        try:
            # Load the complete data
            df = pd.read_csv(file_path)
            print(f"Loaded {len(df)} rows from {os.path.basename(file_path)}")
            
            # Analyze daily data
            daily_analysis = self.analyze_daily_data(df)
            
            return daily_analysis
            
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            return pd.DataFrame()
    
    def save_daily_analysis(self, df: pd.DataFrame, output_path: str) -> str:
        """
        Save the daily analysis to a file.
        
        Args:
            df: DataFrame with daily analysis
            output_path: Path to save the file
            
        Returns:
            Path to saved file
        """
        print(f"Saving daily analysis to: {output_path}")
        df.to_csv(output_path, index=False)
        print(f"Saved {len(df)} daily records")
        return output_path
    
    def process_all_complete_files(self, base_directory: str) -> List[str]:
        """
        Process all complete files and generate daily analysis files.
        
        Args:
            base_directory: Base directory containing complete files
            
        Returns:
            List of paths to saved analysis files
        """
        print("SPX Options Daily Wall Analysis")
        print("=" * 50)
        
        # Find all complete files
        complete_files = self.find_complete_files(base_directory)
        
        if not complete_files:
            print("No complete files found!")
            return []
        
        saved_files = []
        
        for file_path in complete_files:
            try:
                # Process the file
                daily_analysis = self.process_complete_file(file_path)
                
                if not daily_analysis.empty:
                    # Generate output filename
                    base_name = os.path.basename(file_path)
                    output_name = base_name.replace('spx_complete_', 'spx_option_daily_analysis_')
                    output_dir = os.path.dirname(file_path)
                    output_path = os.path.join(output_dir, output_name)
                    
                    # Save the analysis
                    saved_path = self.save_daily_analysis(daily_analysis, output_path)
                    saved_files.append(saved_path)
                    
                    # Print summary
                    print(f"Summary for {base_name}:")
                    print(f"  - Trading days: {len(daily_analysis)}")
                    print(f"  - Date range: {daily_analysis['date'].min()} to {daily_analysis['date'].max()}")
                    print(f"  - Avg daily options: {daily_analysis['total_options'].mean():.0f}")
                    print(f"  - Avg daily OI: {daily_analysis['total_open_interest'].mean():.0f}")
                
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
                continue
        
        print(f"\n=== ANALYSIS COMPLETE ===")
        print(f"Successfully processed {len(saved_files)} files")
        print("Generated analysis files:")
        for file_path in saved_files:
            print(f"  - {file_path}")
        
        return saved_files


def main():
    """Main function to process all complete files."""
    analyzer = WallAnalyzer()
    
    # Base directory for options data
    base_directory = "/Users/<USER>/Downloads/optionhistory"
    
    try:
        saved_files = analyzer.process_all_complete_files(base_directory)
        print(f"\nAll analysis complete! Generated {len(saved_files)} files.")
        
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
