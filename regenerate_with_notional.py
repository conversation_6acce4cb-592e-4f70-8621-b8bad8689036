#!/usr/bin/env python3
"""
Script to regenerate all SPX options daily wall analysis files with notional values.
This replaces call_count and put_count with call_notional and put_notional.
Also adds distance calculations from current price to gamma walls.
"""

import sys
import os
sys.path.insert(0, 'src')

from wall_analyzer import WallAnalyzer


def main():
    print("SPX Options Daily Wall Analysis - ENHANCED UPDATE")
    print("=" * 70)
    print("This script will regenerate all daily analysis files with:")
    print("- Call notional values (Strike × Open Interest × 100)")
    print("- Put notional values (Strike × Open Interest × 100)")
    print("- Total notional values (Call + Put notional)")
    print("- Distance to gamma call wall (gamma_call_wall_strike - current_price)")
    print("- Distance to gamma put wall (current_price - gamma_put_wall_strike)")
    print("- Replaces call_count and put_count with meaningful dollar exposure")
    print()
    
    # Initialize analyzer
    analyzer = WallAnalyzer()
    
    # Base directory for options data
    base_directory = "/Users/<USER>/Downloads/optionhistory"
    
    # Check if base directory exists
    if not os.path.exists(base_directory):
        print(f"Error: Options directory not found: {base_directory}")
        return 1
    
    try:
        # Process all complete files with notional calculations
        saved_files = analyzer.process_all_complete_files(base_directory)
        
        print(f"\n{'='*70}")
        print("NOTIONAL VALUE UPDATE SUMMARY")
        print(f"{'='*70}")
        print(f"Successfully updated {len(saved_files)} analysis files")
        
        if saved_files:
            print("\nUpdated analysis files with notional values:")
            total_size = 0
            for i, file_path in enumerate(saved_files, 1):
                file_size = os.path.getsize(file_path) / 1024  # KB
                total_size += file_size
                print(f"{i:2d}. {os.path.basename(file_path)} ({file_size:.1f} KB)")
            
            print(f"\nTotal files updated: {len(saved_files)}")
            print(f"Total size: {total_size:.1f} KB")
            
            print(f"\n✅ SUCCESS: All analysis files now include enhanced metrics!")
            print("New columns added:")
            print("- call_notional: Dollar exposure in call options")
            print("- put_notional: Dollar exposure in put options")
            print("- total_notional: Combined dollar exposure")
            print("- distance_to_gamma_call_wall: Points from price to gamma call wall")
            print("- distance_to_gamma_put_wall: Points from price to gamma put wall")
            print("\nRemoved columns:")
            print("- call_count, put_count (replaced with more meaningful notional values)")

            # Show sample of what the new metrics represent
            print(f"\nNew Metric Calculations:")
            print("- Notional = Strike Price × Open Interest × 100 (contract multiplier)")
            print("- Represents total dollar exposure if all options were exercised")
            print("- Distance to gamma walls shows how far price is from key gamma levels")
            print("- Positive distance = wall is above/below price as expected")
            print("- More meaningful than simple counts for risk assessment")
            
        else:
            print("❌ No analysis files were updated.")
            print("Please check that complete files exist and are accessible.")
        
    except Exception as e:
        print(f"Error during update: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("\nNotional value update complete!")
    return 0


if __name__ == "__main__":
    exit(main())
