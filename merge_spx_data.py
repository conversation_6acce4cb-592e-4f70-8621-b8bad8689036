#!/usr/bin/env python3
"""
<PERSON>ript to merge SPX options data with SPX index price data for all quarters.
Creates spx_complete files for each quarter.
"""

import sys
import os
sys.path.insert(0, 'src')

from data_merger import DataMerger


def main():
    print("SPX Options and Index Data Merger")
    print("=" * 50)
    print("This script will:")
    print("1. Find all quarters in the options directory")
    print("2. Load SPX index data (1day timeframe)")
    print("3. Merge options data with index data for each quarter")
    print("4. Save complete files as spx_complete_{year}_{quarter}.csv")
    print()
    
    # Initialize merger
    merger = DataMerger()
    
    # Base directory for options data
    base_directory = "/Users/<USER>/Downloads/optionhistory"
    
    # Check if base directory exists
    if not os.path.exists(base_directory):
        print(f"Error: Options directory not found: {base_directory}")
        print("Please ensure the directory exists and contains option data.")
        return 1
    
    try:
        # Process all quarters
        saved_files = merger.process_all_quarters(base_directory)
        
        print(f"\n{'='*50}")
        print("PROCESSING SUMMARY")
        print(f"{'='*50}")
        print(f"Successfully processed {len(saved_files)} quarters")
        
        if saved_files:
            print("\nGenerated files:")
            for i, file_path in enumerate(saved_files, 1):
                file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                print(f"{i:2d}. {os.path.basename(file_path)} ({file_size:.1f} MB)")
                print(f"     {file_path}")
            
            print(f"\nTotal files created: {len(saved_files)}")
            total_size = sum(os.path.getsize(f) for f in saved_files) / (1024 * 1024)
            print(f"Total size: {total_size:.1f} MB")
        else:
            print("No files were generated. Please check:")
            print("1. Options data exists in the expected directories")
            print("2. SPX index data is available")
            print("3. Directory permissions allow file creation")
        
    except Exception as e:
        print(f"Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("\nMerging complete!")
    return 0


if __name__ == "__main__":
    exit(main())
