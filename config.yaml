# Data Configuration File
# Defines instrument types, directory formats, file naming conventions, and data formats

instruments:
  options:
    instrument_type: "options"
    directory_format: "/Users/<USER>/Downloads/optionhistory/{year}_{quarter}_optionchain_{random}"
    file_name_format: "{ticker}_{year}_{quarter}_option_chain.txt"
    file_type_format: "txt"
    data_format:
      columns:
        - "date"
        - "Strike"
        - "Expiry Date"
        - "Call/Put"
        - "Last Trade Price"
        - "Bid Price"
        - "Ask Price"
        - "Bid Implied Volatility"
        - "Ask Implied Volatility"
        - "Open Interest"
        - "Volume"
        - "Delta"
        - "Gamma"
        - "Vega"
        - "Theta"
        - "Rho"
      separator: ","
      header: true
    
  securities:
    instrument_type: "securities"
    directory_format: "/Users/<USER>/Downloads/systems/strategy_package/data/securities"
    file_name_format: "{ticker}_full_{timeperiod}.txt"
    file_type_format: "txt"
    timeperiods:
      - "1day"
      - "1min"
      - "5min"
      - "30min"
    data_format:
      columns:
        - "date"
        - "open"
        - "high"
        - "low"
        - "close"
      separator: ","
      header: false

# Global settings
settings:
  default_instrument: "options"
  data_encoding: "utf-8"
