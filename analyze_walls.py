#!/usr/bin/env python3
"""
<PERSON><PERSON>t to analyze SPX options data and calculate daily put/call walls and gamma walls.
Processes all spx_complete files and creates spx_option_daily_analysis files.
"""

import sys
import os
sys.path.insert(0, 'src')

from wall_analyzer import WallAnalyzer


def main():
    print("SPX Options Daily Wall Analysis")
    print("=" * 50)
    print("This script will:")
    print("1. Find all spx_complete_*.csv files in the options directory")
    print("2. For each day, calculate:")
    print("   - Put/Call walls (highest open interest strikes)")
    print("   - Gamma walls (highest gamma exposure strikes)")
    print("   - Daily aggregates (total gamma, vega, open interest)")
    print("3. Save results as spx_option_daily_analysis_*.csv files")
    print()
    
    # Initialize analyzer
    analyzer = WallAnalyzer()
    
    # Base directory for options data
    base_directory = "/Users/<USER>/Downloads/optionhistory"
    
    # Check if base directory exists
    if not os.path.exists(base_directory):
        print(f"Error: Options directory not found: {base_directory}")
        print("Please ensure the directory exists and contains complete files.")
        return 1
    
    try:
        # Process all complete files
        saved_files = analyzer.process_all_complete_files(base_directory)
        
        print(f"\n{'='*50}")
        print("ANALYSIS SUMMARY")
        print(f"{'='*50}")
        print(f"Successfully analyzed {len(saved_files)} quarters")
        
        if saved_files:
            print("\nGenerated analysis files:")
            total_size = 0
            for i, file_path in enumerate(saved_files, 1):
                file_size = os.path.getsize(file_path) / 1024  # KB
                total_size += file_size
                print(f"{i:2d}. {os.path.basename(file_path)} ({file_size:.1f} KB)")
                print(f"     {file_path}")
            
            print(f"\nTotal files created: {len(saved_files)}")
            print(f"Total size: {total_size:.1f} KB")
            
            # Show sample of what the analysis contains
            print(f"\nEach daily analysis file contains:")
            print("- Date and SPX OHLC prices")
            print("- Put wall strike and open interest")
            print("- Call wall strike and open interest") 
            print("- Gamma put wall strike and exposure")
            print("- Gamma call wall strike and exposure")
            print("- Total gamma, vega, open interest, volume")
            print("- Option counts and unique strikes")
            
        else:
            print("No analysis files were generated. Please check:")
            print("1. Complete files exist (spx_complete_*.csv)")
            print("2. Complete files contain valid options data")
            print("3. Directory permissions allow file creation")
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("\nWall analysis complete!")
    return 0


if __name__ == "__main__":
    exit(main())
