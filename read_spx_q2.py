#!/usr/bin/env python3
"""
Script to read SPX options data for 2023 Q2 using the configuration system.
"""

import sys
import os
sys.path.insert(0, 'src')

from config import Config<PERSON>anager
from data_reader import DataReader

def main():
    print("SPX Options Data Reader for 2023 Q2")
    print("=" * 50)
    
    # Create fresh instances to avoid caching issues
    config = ConfigManager('config.yaml')
    reader = DataReader()
    
    # Show expected file location
    directory = config.format_directory_path('options', year='2023', quarter='Q2')
    filename = config.format_file_name('options', ticker='SPX', year='2023', quarter='Q2')
    full_path = config.get_full_file_path('options', ticker='SPX', year='2023', quarter='Q2')
    
    print(f"Expected directory: {directory}")
    print(f"Expected filename: {filename}")
    print(f"Full expected path: {full_path}")
    print()
    
    # Check if directory exists
    if os.path.exists(directory):
        print(f"✓ Directory exists: {directory}")
        files_in_dir = os.listdir(directory)
        print(f"Files in directory: {files_in_dir}")
        print()
        
        # Try to read the data
        print("Attempting to read SPX options data...")
        df = reader.read_options_data('SPX', '2023', 'Q2')
        
        if df is not None:
            # Get summary
            summary = reader.get_data_summary(df, 'options')
            
            print("\n=== DATA SUMMARY ===")
            print(f"Total rows: {summary['total_rows']}")
            print(f"Columns: {summary['columns']}")
            print(f"Date range: {summary.get('date_range', 'N/A')}")
            print(f"Memory usage: {summary['memory_usage']}")
            
            if 'call_put_distribution' in summary:
                print(f"Call/Put distribution: {summary['call_put_distribution']}")
            
            if 'strike_range' in summary:
                print(f"Strike range: {summary['strike_range']['min']} - {summary['strike_range']['max']}")
            
            print("\n=== FIRST 5 ROWS ===")
            print(df.head().to_string())
            
            print(f"\n=== DATA LOADED SUCCESSFULLY ===")
            print(f"SPX options data for 2023 Q2 has been loaded with {len(df)} rows")
            
        else:
            print("No data could be loaded from the files found.")
    else:
        print(f"✗ Directory does not exist: {directory}")
        print("\nTo use this script, create the directory and place your SPX options file there:")
        print(f"mkdir -p '{directory}'")
        print(f"# Then copy your SPX options file as: {filename}")

if __name__ == "__main__":
    main()
