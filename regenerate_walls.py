#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to regenerate all SPX options daily wall analysis files with corrected logic.
This will overwrite the existing analysis files with properly differentiated put/call walls.
"""

import sys
import os
sys.path.insert(0, 'src')

from wall_analyzer import WallAnalyzer


def main():
    print("SPX Options Daily Wall Analysis - REGENERATION")
    print("=" * 60)
    print("This script will regenerate all daily analysis files with corrected logic:")
    print("- Put walls: Below current SPX price (support levels)")
    print("- Call walls: Above current SPX price (resistance levels)")
    print("- Gamma walls: Similarly positioned based on current price")
    print("- Ensures put and call walls are always different strikes")
    print()
    
    # Initialize analyzer
    analyzer = WallAnalyzer()
    
    # Base directory for options data
    base_directory = "/Users/<USER>/Downloads/optionhistory"
    
    # Check if base directory exists
    if not os.path.exists(base_directory):
        print(f"Error: Options directory not found: {base_directory}")
        return 1
    
    try:
        # Process all complete files with corrected logic
        saved_files = analyzer.process_all_complete_files(base_directory)
        
        print(f"\n{'='*60}")
        print("REGENERATION SUMMARY")
        print(f"{'='*60}")
        print(f"Successfully regenerated {len(saved_files)} analysis files")
        
        if saved_files:
            print("\nRegenerating analysis files with corrected wall logic:")
            total_size = 0
            for i, file_path in enumerate(saved_files, 1):
                file_size = os.path.getsize(file_path) / 1024  # KB
                total_size += file_size
                print(f"{i:2d}. {os.path.basename(file_path)} ({file_size:.1f} KB)")
            
            print(f"\nTotal files regenerated: {len(saved_files)}")
            print(f"Total size: {total_size:.1f} KB")
            
            print(f"\n✅ SUCCESS: All wall analysis files have been regenerated!")
            print("Key improvements:")
            print("- Put walls now properly identify support levels below current price")
            print("- Call walls now properly identify resistance levels above current price")
            print("- Gamma walls are positioned relative to current SPX price")
            print("- No more duplicate wall strikes between puts and calls")
            
        else:
            print("❌ No analysis files were regenerated.")
            print("Please check that complete files exist and are accessible.")
        
    except Exception as e:
        print(f"Error during regeneration: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("\nWall analysis regeneration complete!")
    return 0


if __name__ == "__main__":
    exit(main())
